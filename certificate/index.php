

<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous" />
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" />


        <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/0.4.1/html2canvas.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.5/jspdf.min.js"></script>

        <link rel="stylesheet" href="style.css" />
        <title>Certificate</title>
    </head>
    <body>
        <div class="container">
            <div class="row w-100">
                <div class="col-lg-12 col-md-8 px-3 py-2">
                    <div class="crad d-flex justify-content-center alinge-items-center">
                        <div class="toolbar no-print">
                            <button class="btn btn-info" onclick="window.print()">
                                Print Certificate
                            </button>
                            <button class="btn btn-info" id="downloadPDF">Download PDF</button>
                        </div>
                        
                        <div class="cert-container print-m-0">
                            <div id="content2" class="cert">
                                <img src="frame2.jpg" class="cert-bg" alt="" />
                                <div class="cert-content">
                                        <div>&nbsp;</div> <div>&nbsp;</div> <div>&nbsp;</div>
                                        <div>&nbsp;</div> <div>&nbsp;</div> 

                                    <!-- <h1 style="font-family: 'UnifrakturCook', cursive;">Hayward University</h1> -->
                                    <span style="font-size: 24px;color: black;">Metairie, Louisiana</span>
                                    <br />
                                    <span style="font-size: 16px;color: black;padding: 0 50px">Be it known that Hayward University in recognition of the successful completion of the requisite  course of study has conferred upon</span>
                                    <br />
                                    <!--<span style="font-size: 32px;color: black;">Srinibas Dannana</span>-->
                                    <br />
                                    <span style="font-size: 32px;color: black;"><?php echo $fullname = $_GET['value']; ?></span>
                                    <br />
                                    <span class="other-font">
                                        <i><b style="color: black;font-size: 18px">the degree of</b></i>
                                    </span>
                                    <br />
                                    <span style="font-size: 32px;color: black;"><b>Masters of Theology</b></span>
                                    <br />
                                    <small style="color: black;font-size: 18px">
                                       with all the rights and privileges thereto appertaining. In testimony whereof the seal of the school and the signatures of its officers have been duly affixed this 
                                       
                                       <!-- twenty-fifth day of january in the year two thousand seven -->
                                       <!-- For date in word format start here -->
                                       
                                       <?php
                function numberToWords($number) {
                    $words = [
                        0 => '', 1 => 'First', 2 => 'Second', 3 => 'Third', 4 => 'Fourth',
                        5 => 'fifth', 6 => 'sixth', 7 => 'seventh', 8 => 'eighth', 9 => 'ninth',
                        10 => 'Tenth', 11 => 'Eleventh', 12 => 'Twelfth', 13 => 'thirteenth',
                        14 => 'fourteenth', 15 => 'fifteenth', 16 => 'sixteenth', 17 => 'seventeenth',
                        18 => 'eighteenth', 19 => 'nineteenth', 20 => 'twentieth',
                        21 => 'twenty-first', 22 => 'twenty-second', 23 => 'twenty-third',
                        24 => 'twenty-fourth', 25 => 'twenty-fifth', 26 => 'twenty-sixth',
                        27 => 'twenty-seventh', 28 => 'twenty-eighth', 29 => 'twenty-ninth',
                        30 => 'thirtieth', 31 => 'thirty-first'
                    ];
                    return $words[$number];
                }

                function yearToWords($year) {
                    $tens = ['', 'ten', 'Twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
                    $teens = ['eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
                    $units = ['zero', 'one', 'Two', 'three', 'Four', 'five', 'six', 'seven', 'eight', 'nine'];
                    $thousands = intval($year / 1000);
                    $hundreds = intval(($year % 1000) / 100);
                    $remainder = $year % 100;
                    $yearInWords = $units[$thousands] . " Thousand";
                    if ($hundreds > 0) {
                        $yearInWords .= " and " . $units[$hundreds] . " hundred";
                    }
                    if ($remainder > 0) {
                        if ($remainder < 10) {
                            $yearInWords .= " and " . $units[$remainder];
                        } elseif ($remainder < 20 && $remainder > 10) {
                            $yearInWords .= " and " . $teens[$remainder - 11];
                        } else {
                            $yearInWords .= " and " . $tens[intval($remainder / 10)];
                            if ($remainder % 10 > 0) {
                                $yearInWords .= " " . $units[$remainder % 10];
                            }
                        }
                    }
                    return $yearInWords;
                }

                function getDateInWords() {
                    $day = date('j');
                    $month = date('F');
                    $year = date('Y');
                    return numberToWords($day) . " day of " . $month . " in the year " . yearToWords($year);
                }

                echo "" . getDateInWords();
            ?>
            
                    <!-- For date in word format ends here -->
                                    </small>
                                    
                                    <p id="demo"></p>
                                    <!--<script>
                                        const d = new Date();
                                        document.getElementById("demo").innerHTML = d;
                                        echo date("l, F j, Y");
                                    </script>-->

                                    <br />
                                    <br /><br />
                                    <div class="col-md-12">
                                        <!-- <div class="col-md-4 bottom-txt">
                                            
                                        </div>
                                        <div class="col-md-4">
                                            <img src="logo.jpeg" style="width: 30%;margin-top: -34px;">
                                        </div>
                                        <div class="col-md-4 bottom-txt">
                                            
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
  

        <script type="text/javascript">




          $("#downloadPDF").click(function () {
  // $("#content2").addClass('ml-215'); // JS solution for smaller screen but better to add media queries to tackle the issue
  getScreenshotOfElement(
    $("div#content2").get(0),
    0,
    0,
    $("#content2").width() + 45,  // added 45 because the container's (content2) width is smaller than the image, if it's not added then the content from right side will get cut off
    $("#content2").height() + 30, // same issue as above. if the container width / height is changed (currently they are fixed) then these values might need to be changed as well.
    function (data) {
      var pdf = new jsPDF("l", "pt", [
        $("#content2").width(),
        $("#content2").height(),
      ]);

      pdf.addImage(
        "data:image/png;base64," + data,
        "PNG",
        0,
        0,
        $("#content2").width(),
        $("#content2").height()
      );
      pdf.save("certificte.pdf");
    }
  );
});

// this function is the configuration of the html2cavas library (https://html2canvas.hertzen.com/)
// $("#content2").removeClass('ml-215'); is the only custom line here, the rest comes from the library.
function getScreenshotOfElement(element, posX, posY, width, height, callback) {
  html2canvas(element, {
    onrendered: function (canvas) {
      // $("#content2").removeClass('ml-215');  // uncomment this if resorting to ml-125 to resolve the issue
      var context = canvas.getContext("2d");
      var imageData = context.getImageData(posX, posY, width, height).data;
      var outputCanvas = document.createElement("canvas");
      var outputContext = outputCanvas.getContext("2d");
      outputCanvas.width = width;
      outputCanvas.height = height;

      var idata = outputContext.createImageData(width, height);
      idata.data.set(imageData);
      outputContext.putImageData(idata, 0, 0);
      callback(outputCanvas.toDataURL().replace("data:image/png;base64,", ""));
    },
    width: width,
    height: height,
    useCORS: true,
    taintTest: false,
    allowTaint: false,
  });
}



        </script>



    
      </body>
</html>
