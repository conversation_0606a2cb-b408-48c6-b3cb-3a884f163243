@import url('https://fonts.googleapis.com/css2?family=Dancing+Script&family=Jost:wght@500&family=Open+Sans:ital,wght@1,300&family=Oswald&family=Roboto:wght@300&family=Ruthie&family=UnifrakturCook:wght@700&family=UnifrakturMaguntia&display=swap');
* {
margin: 0;
padding: 0;
box-sizing: border-box;
font-family: 'UnifrakturCook', cursive;
}
body {
    background-color: white;
}


@media print{
    .no-print, .no-print *{
      display: none !important;
    } 
    .print-m-0{
      margin: 0 !important;
    }
  } 
  
  .btn{
    padding: 10px 17px; 
    border-radius: 3px; 
    background: #f4b71a; 
    border: none; 
    font-size: 12px; 
    margin: 10px 5px;
  }
  
  .toolbar{
    background: #333; 
    width: 100vw; 
    position: fixed; 
    left: 0; 
    top: 0; 
    text-align: center;
  }
  
  .cert-container {
    margin:65px 0 10px 0; 
    width: 100%; 
    display: flex; 
    justify-content: center;
  }
  
  .cert {
    width:800px; 
    height:600px; 
    padding:15px 20px; 
    text-align:center; 
    position: relative; 
    z-index: -1;
  }
  
  .cert-bg {
    position: absolute; 
    left: 0px; 
    top: 0; 
    z-index: -1;
    width: 100%;
  }
  
  .cert-content {
    width:750px; 
    height:470px; 
    padding:35px 35px 0px 35px; 
    text-align:center;
    font-family: Arial, Helvetica, sans-serif;
    
  }
  
  h1 {
    font-size:44px;
  }
  
  p {
    font-size:25px;
  }
  
  small {
    font-size: 14px;
    line-height: 12px;
  }
  
  .bottom-txt {
    padding: 12px 5px; 
    display: flex; 
    justify-content: space-between;
    font-size: 16px;
  }
  
  .bottom-txt * {
    white-space: nowrap !important;
  }
  
  .other-font {
    font-family: Cambria, Georgia, serif;
    font-style: italic;
  }
  
  .ml-215 {
    margin-left: 215px;
  }