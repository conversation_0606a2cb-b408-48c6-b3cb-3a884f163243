<?php
// Use the same database connection file as your website
include "connect.php";

// Set the content type to JSON for the response
header('Content-Type: application/json');

// Get the user's message from the incoming JSON request from the chat widget
$data = json_decode(file_get_contents('php://input'), true);
$userMessage = $data['message'] ?? '';

// Default response if no match is found
$responseText = "I'm sorry, I don't have information about that. Please try asking in a different way.";

if (!empty($userMessage) && isset($con)) {
    // Sanitize the user input
    $sanitizedMessage = mysqli_real_escape_string($con, $userMessage);
    
    // Split the user's message into individual words
    $keywords = preg_split('/\s+/', $sanitizedMessage);
    
    $queryConditions = [];
    foreach ($keywords as $word) {
        // We only care about words with 2 or more letters
        if(strlen($word) >= 2) {
            // ✅ FIXED: Use REGEXP to match whole words only.
            // [[:<:]] and [[:>:]] are word boundaries.
            $queryConditions[] = "keywords REGEXP '[[:<:]]" . $word . "[[:>:]]'";
        }
    }

    if (!empty($queryConditions)) {
        // Build the final SQL query
        $sql = "SELECT response FROM knowledge_base WHERE " . implode(' OR ', $queryConditions) . " ORDER BY id DESC LIMIT 1";
        
        $result = mysqli_query($con, $sql);

        if ($result && mysqli_num_rows($result) > 0) {
            // If a match is found, fetch the response
            $row = mysqli_fetch_assoc($result);
            $responseText = $row['response'];
        }
    }
}

// Send the final response back to the chat widget as a JSON object
echo json_encode(['reply' => $responseText]);
?>