<style>
    .footer .footer-bottom .module-body ul li a:before
    {
        display: none !important;
    }
    .footer .footer-bottom
    {
         padding-top: 40px !important;
         padding-bottom: 0px !important;
    }
    .payment-methods
    {
        margin-left: -75px;
    }
    @media (min-width: 767px)
    {
        .footer .footer-bottom .module-body ul li a
        {
            margin-left: 0px;
        }
    }
</style>
<footer id="footer" class="footer color-bg">
    <style>
        @media (max-width: 767px)
        {
            .news { width: 205px; margin-left: -55px; }
            .nsw { margin-left: -38px; }
        }
    </style>
    <div class="footer-bottom" style="padding-left: 80px; padding-right: 45px;background-color: #ffffff">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="col-md-1 nsw" style="padding: 10px 0px 0px 0px;">
                        <p><strong style="font-size: 16px">Newsletter</strong></p>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control unicase-form-control text-input news" style="border: 1px solid;" placeholder="Enter Your Email Address">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="clearfix pull-right">
                            <button type="submit" class="btn-upper btn btn-primary" style="height: 42px;background: #202020;">Subscribe</button>
                        </div>
                    </div>
                    <style>
                        @media (max-width: 767px)
                        {
                            .scl { margin-top: 62px; margin-left: -62px; }
                            .sl { margin: 0px 11px 0px 11px !important; }
                        }
                    </style>
                    <div class="col-md-6 no-padding social scl">
                        <style>
                            @import url(https://fonts.googleapis.com/css?family=Lato);
                            @import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.css);
                            ul { padding:0; list-style: none; }
                            .footer-social-icons { width: 350px; display:block; margin: 0 auto; }
                            .social-icons li { vertical-align: top; display: inline; }
                            .social-icons a { color: #fff; text-decoration: none; }
                            .fa-facebook { padding:10px 14px; transition: .5s; background-color: #322f30; }
                            .fa-facebook:hover { background-color: #3d5b99; }
                            .fa-twitter { padding:10px 12px; transition: .5s; background-color: #322f30; }
                            .fa-twitter:hover { background-color: #00aced; }
                            .fa-youtube-play { padding:10px 14px; transition: .5s; background-color: #322f30; }
                            .fa-youtube-play:hover { background-color: red; }
                            .fa-pinterest { padding:10px 14px; transition: .5s; background-color: #322f30; }
                            .fa-pinterest:hover { background-color: #cb2027; }
                            .fa-vk { padding:10px 14px; transition: .5s; background-color: #322f30; }
                            .fa-vk:hover { background-color: #6383a8; }
                            .fa-instagram { padding:10px 14px; transition: .5s; background-color: #322f30; }
                            .fa-instagram:hover { background-color: #0193ff; }
                        </style>
                        <div class="footer-social-icons">
                            <ul class="social-icons">
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-facebook" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-youtube-play" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-pinterest" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-vk" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-instagram" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a href="javascript:void(0);" class="social-icon"> <i class="fa fa-twitter" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div>&nbsp;</div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700;font-size: 14px;">UNIVERSITY INFORMATION</h4>
                    </div>
                    <div class="module-body">
                        <ul class="list-unstyled">
                             <li><a href="contact_us.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Contact Us</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">USEFULL LINKS</h4>
                    </div>
                    <div class="module-body">
                        <ul class="list-unstyled">
                             <li><a href="faqs_covid_19.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">FAQs about COVID-19</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">HAYWARD RESOURCES</h4>
                    </div>
                    <div class="module-body">
                        <ul class="list-unstyled">
                             <li><a href="sitemap.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Sitemap</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">TOP SEARCHES</h4>
                    </div>
                    <div class="module-body">
                        <ul class="list-unstyled">
                             <li><a href="friendly_staff.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Friendly Staff</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">Download App!</h4>
                    </div>
                    <div class="module-body">
                        <img src="assets/images/aap.png">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="copyright-bar" style="background: #fafafa;padding-bottom: 0px;">
        <div class="container-fluid" style="padding:0px 0px 0px 0px">
            <div class="col-xs-12 col-sm-12 no-padding">
                 <img src="assets/images/payment.jpg">
            </div>
        </div>
    </div>
    <div class="copyright-bar" style="background: #020303;height: 60px;">
       <p class="desktop" style="color: white; font-family: 'Open Sans', sans-serif; text-align: center">
            Copyright © 2020-2022. Hayward University . All Rights Reserved. Design And Developed By <a target="_blank" href="http://www.iwebnext.com" style="color: white; font-weight: 700;">IWebNext</a>.
        </p>      
    </div>
</footer>

<div class="float-sm">
     </div>

<style>
    /* Launcher Icon Button */
    #chat-launcher-btn {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background-color: #202020; /* Matches your site's theme */
        border: none;
        border-radius: 50%;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        cursor: pointer;
        z-index: 9998;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: transform 0.2s ease-in-out;
    }

    #chat-launcher-btn:hover {
        transform: scale(1.1);
    }

    #chat-launcher-btn svg {
        width: 32px;
        height: 32px;
        fill: #ffffff;
    }

    /* Chat Widget Container */
    .chat-widget {
        position: fixed;
        bottom: 90px; /* Position above the launcher icon */
        right: 20px;
        width: 350px;
        border: 1px solid #ccc;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        font-family: sans-serif;
        z-index: 9999;
        background-color: #fff;
        /* Animation properties */
        transform-origin: bottom right;
        transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
        /* Initially hidden */
        transform: scale(0);
        opacity: 0;
        pointer-events: none;
    }

    /* Class to show the widget */
    .chat-widget.open {
        transform: scale(1);
        opacity: 1;
        pointer-events: auto;
    }

    .chat-header {
        position: relative; /* Needed for positioning the close button */
        background-color: #202020;
        color: white;
        padding: 10px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        text-align: center;
    }
    
    /* Close Button */
    #chat-close-btn {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #ffffff;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
    }

    .chat-body {
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        display: flex;
        flex-direction: column;
        background-color: #f9f9f9;
    }
    .chat-message {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 20px;
        max-width: 80%;
        line-height: 1.4;
    }
    .user-message {
        background-color: #ffda00;
        color: black;
        align-self: flex-end;
    }
    .bot-message {
        background-color: #e9e9eb;
        color: black;
        align-self: flex-start;
    }
    .chat-footer {
        display: flex;
        padding: 10px;
        border-top: 1px solid #ccc;
    }
    .chat-footer input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 20px;
    }
    .chat-footer button {
        margin-left: 10px;
        padding: 8px 12px;
        border: none;
        background-color: #202020;
        color: white;
        border-radius: 20px;
        cursor: pointer;
    }
</style>

<button id="chat-launcher-btn">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"></path>
    </svg>
</button>

<div class="chat-widget" id="chat-widget">
    <div class="chat-header">
        <h3>Chat Assistant</h3>
        <button id="chat-close-btn">&times;</button>
    </div>
    <div class="chat-body" id="chat-body">
        <div class="chat-message bot-message">Hello! How can I help you today?</div>
    </div>
    <div class="chat-footer">
        <input type="text" id="user-input" placeholder="Type a message...">
        <button id="send-btn">Send</button>
    </div>
</div>

<script>
    (function() {
        // --- Get all the necessary elements ---
        const launcherBtn = document.getElementById('chat-launcher-btn');
        const chatWidget = document.getElementById('chat-widget');
        const closeBtn = document.getElementById('chat-close-btn');
        const chatBody = document.getElementById('chat-body');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');

        // --- Event Listeners for showing and hiding the widget ---
        launcherBtn.addEventListener('click', () => {
            chatWidget.classList.toggle('open');
        });

        closeBtn.addEventListener('click', () => {
            chatWidget.classList.remove('open');
        });

        // --- Event Listeners for sending messages ---
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // --- Core message sending and receiving functions ---
        function sendMessage() {
            const message = userInput.value.trim();
            if (message === '') return;

            appendMessage(message, 'user');
            userInput.value = '';
            userInput.focus();

            fetch('chatbot_backend.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                const reply = data.reply || 'Sorry, an error occurred.';
                appendMessage(reply, 'bot');
            })
            .catch(error => {
                console.error('Fetch error:', error);
                appendMessage('Sorry, I couldn\'t connect to the chat server.', 'bot');
            });
        }

        function appendMessage(message, sender) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message', `${sender}-message`);
            messageElement.innerText = message;
            chatBody.appendChild(messageElement);
            chatBody.scrollTop = chatBody.scrollHeight;
        }
    })();
</script>

</body>
</html>