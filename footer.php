
<style>
    .footer .footer-bottom .module-body ul li a:before
    {
        display: none !important;
    }
    .footer .footer-bottom
    {
         padding-top: 40px !important;
         padding-bottom: 0px !important;
    }


    .payment-methods
    {
        margin-left: -75px;
    }

    @media (min-width: 767px)
    {
        .footer .footer-bottom .module-body ul li a
        {
            margin-left: 0px;
        }
    }


</style>
<footer id="footer" class="footer color-bg">
    <style>
        @media (max-width: 767px)
        {
            .news
            {
                width: 205px;
                margin-left: -55px;
            }
            .nsw
            {
                margin-left: -38px;
            }

        }

    </style>

    <div class="footer-bottom" style="padding-left: 80px; padding-right: 45px;background-color: #ffffff">
        <div class="container-fluid">
            <div class="row">

                <div class="col-md-12"> 
                    <!-- 
                    <div class="col-md-1 nsw" style="padding: 10px 0px 0px 0px;">
                        <p><strong style="font-size: 16px">Newsletter</strong></p>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <input type="text" class="form-control unicase-form-control text-input news" style="border: 1px solid;" placeholder="Enter Your Email Address">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="clearfix pull-right">
                            <button type="submit" class="btn-upper btn btn-primary" style="height: 42px;background: #202020;">Subscribe</button>
                        </div>
                    </div>
                    -->

<style>
    @media (max-width: 767px)
    {
        .scl
        {
            margin-top: 62px;
            margin-left: -62px;
        }
        .sl
        {
            margin: 0px 11px 0px 11px !important;
        }
    }


</style>
                    <div class="col-md-6 no-padding social scl">
                        <!--<ul class="link">
                            <li class="fb pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #3b5998 !important;border-radius: 50px;" title="Facebook"></a></li>
                            <li class="tw pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #00acee !important;border-radius: 50px;" title="Twitter"></a></li>
                            <li class="googleplus pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #db4a39 !important;border-radius: 50px;" title="GooglePlus"></a></li>
                            <li class="rss pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #ee802f !important;border-radius: 50px;" title="RSS"></a></li>
                            <li class="pintrest pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #e60023 !important;border-radius: 50px;" title="PInterest"></a></li>
                            <li class="linkedin pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #0e76a8 !important;border-radius: 50px;" title="Linkedin"></a></li>
                            <li class="youtube pull-left"><a target="_blank" rel="nofollow" href="javascript:void(0);" style="background: #ff0000 !important;border-radius: 50px;" title="Youtube"></a></li>
                        </ul>-->
                             <style>
    @import url(https://fonts.googleapis.com/css?family=Lato);
    @import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.css);


    ._12 {
        font-size: 1.2em;
    }
    ._14 {
        font-size: 1.4em;
    }
    ul {
        padding:0;
        list-style: none;
    }
    .footer-social-icons {
        width: 350px;
        display:block;
        margin: 0 auto;
    }
    .social-icon {
        color: #fff;
    }
    ul.social-icons {
        margin-top: 10px;
    }
    .social-icons li {
        vertical-align: top;
        display: inline;
        height: 100px;
    }
    .social-icons a {
        color: #fff;
        text-decoration: none;
    }
    .fa-facebook {
        padding:10px 14px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-facebook:hover {
        background-color: #3d5b99;
    }
    .fa-twitter {
        padding:10px 12px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-twitter:hover {
        background-color: #00aced;
    }

    .fa-youtube-play {
        padding:10px 14px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-youtube-play:hover {
        background-color: red;
    }

    .fa-pinterest {
        padding:10px 14px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-pinterest:hover {
        background-color: #cb2027;
    }

    .fa-vk {
        padding:10px 14px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-vk:hover {
        background-color: #6383a8;
    }

    .fa-instagram {
        padding:10px 14px;
        -o-transition:.5s;
        -ms-transition:.5s;
        -moz-transition:.5s;
        -webkit-transition:.5s;
        transition: .5s;
        background-color: #322f30;
    }
    .fa-instagram:hover {
        background-color: #0193ff;
    }
</style>

                        <div class="footer-social-icons">
                            <ul class="social-icons">
                                <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://www.facebook.com/profile.php?id=61567844901629" class="social-icon"> <i class="fa fa-facebook" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://www.youtube.com/channel/UC_aN4KTZg8RvwvwQPycUMNA" class="social-icon"> <i class="fa fa-youtube-play" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://www.pinterest.com/mohansaigupta31/_created/" class="social-icon"> <i class="fa fa-pinterest" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <!-- <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://vk.com/id885608247" class="social-icon"> <i class="fa fa-vk" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li> -->
                                <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://www.instagram.com/hu202_41/profilecard/?igsh=NHUxMDQ5MWF1cWxy" class="social-icon"> <i class="fa fa-instagram" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                                <li class="sl" style="margin-left: 5px;"><a target="_blank" href="https://x.com/HaywardUnivers" class="social-icon"> <i class="fa fa-twitter" style="text-align: center !important;;border-radius: 50%;margin-left: -10px;"></i></a></li>
                            </ul>
                        </div>

                    </div>

                </div>

                <div>&nbsp;</div>

                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700;font-size: 14px;">UNIVERSITY INFORMATION</h4>
                    </div>
                    <!-- /.module-heading -->

                    <div class="module-body">
                        <ul class="list-unstyled" style="font-family: 'Open Sans', sans-serif; color: white; font-weight: 700; font-size: 25px;">
                            <li><a href="referrals.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Referrals</a></li>
                            <li><a href="top_searches.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Top Searches</a></li>
                            <li><a href="testimonial.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Testimonials</a></li>
                            <li><a href="report_security_issue.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Report Security Issue</a></li>
                            <li><a href="event.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Events</a></li>
                            <li><a href="news.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">News</a></li>
                            <li><a href="cookies_policy.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Cookies Policy</a></li>
                            <li><a href="privacy_policy.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Privacy Policy</a></li>
                            <li><a href="intellectual_property_policy.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Intellectual Property Policy</a></li>
                            <li><a href="terms_and_conditions.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Terms and Conditions</a></li>
                            <li><a href="disclaimer.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Disclaimer</a></li>
                            <li><a href="contact_us.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Contact Us</a></li>
                        </ul>
                    </div>
                    <!-- /.module-body -->
                </div>
                <!-- /.col -->

                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">USEFUL LINKS</h4>
                    </div>
                    <!-- /.module-heading -->

                    <div class="module-body">
                        <ul class="list-unstyled" style="font-family: 'Open Sans', sans-serif; color: black; font-weight: 700; font-size: 25px;">
                            <li><a href="contact_information.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Contact Information</a></li>
                            <li><a href="payment_methods.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Payment Methods</a></li>
                            <li><a href="return_policy.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Return Policies</a></li>
                            <li><a href="faqs.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">FAQ & Support</a></li>
                            <li><a href="top_courses.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Top Courses</a></li>
                            <li><a href="tech_support1.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Tech Support</a></li>
                            <li><a href="career_assistance.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Career Assistance</a></li>
                            <li><a href="referrals.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Referrals</a></li>
                            <li><a href="terms_and_conditions.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Terms and Conditions</a></li>
                            <li><a href="scholarships.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Scholarships</a></li>
                            <li><a href="student_services.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Student Services</a></li>
                            <li><a href="faqs_covid_19.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">FAQs about COVID-19</a></li>
                        </ul>
                    </div>
                    <!-- /.module-body -->
                </div>
                <!-- /.col -->

                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">HAYWARD RESOURCES</h4>
                    </div>
                    <!-- /.module-heading -->

                    <div class="module-body">
                        <ul class="list-unstyled">
                            <li><a href="academic-regulations.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Academics</a></li>
                            <li><a href="admission_process.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Admissions</a></li>
                            <li><a href="processing_fee.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Processing Fees</a></li>
                            <li><a href="financial_assistance.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Financial Assistance</a></li>
                            <li><a href="student_services.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Student Services</a></li>
                            <li><a href="testimonial.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Testimonials</a></li>
                            <li><a href="report_security_issue.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Report Security Issue</a></li>
                            <li><a href="faqs.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">FAQ's</a></li>
                            <li><a href="help-center.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Help Center</a></li>
                            <li><a href="about_the_university.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">AboutUs</a></li>
                            <li><a href="contact_us.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Contact Us</a></li>
                            <!-- <li><a href="sitemap.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Sitemap</a></li> -->
                            <li><a href="faculty_members.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Friendly Staff</a></li>
                        </ul>
                    </div>
                    <!-- /.module-body -->
                </div>
                <!-- /.col -->
                <div class="col-xs-12 col-sm-6 col-md-2">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">TOP SEARCHES</h4>
                    </div>
                    <!-- /.module-heading -->

                    <div class="module-body">
                        <ul class="list-unstyled">
                            <li><a href="fastest_mba.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Fastest Theology</a></li>
                            <li><a href="study_at_your_own_pace.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Study at your own pace</a></li>
                            <li><a href="tuition_free.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Tuition Fee</a></li>
                            <li><a href="financial_aid.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Financial Aid</a></li>
                            <li><a href="scholarships.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Scholarships</a></li>
                            <li><a href="transfer_credits.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Transfer Credits</a></li>
                            <li><a href="life_experience_credits.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Life Experience Credits</a></li>
                            <li><a href="study_from_anywhere_and_anytime.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Study from anywhere and anytime</a></li>
                            <li><a href="100_percent_online.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">100% Online</a></li>
                            <li><a href="whatsapp_instant_help.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Whatsapp - Instant Help</a></li>
                            <li><a href="classroom_mobile_app.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Classroom App</a></li>
                            <!-- <li><a href="faculty_members.php" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: #666;font-weight: 200;">Friendly Staff</a></li>-->
                        </ul>
                    </div>
                    <!-- /.module-body -->
                </div>

                <div class="col-xs-12 col-sm-6 col-md-3">
                    <div class="module-heading">
                        <h4 class="module-title" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; color: black; font-weight: 700; font-size: 14px;">Download App!</h4>
                    </div>
                    <!-- /.module-heading -->

                    <div class="module-body">
                        <!-- <ul class="list-unstyled">
                            <li><a href="javascript:void(0);" style="font-family: OpenSans,Arial,Helvetica,sans-serif !important; font-size: 12px;line-height: 20px; color: black;">Save $3 with App & New User Only</a></li>
                        </ul> -->
                        <img src="assets/images/hayward_qr.png">
                        
                    </div>

                    

                    <!-- /.module-body -->
                </div>
            </div>
        </div>
    </div>

    <div class="copyright-bar" style="background: #fafafa;padding-bottom: 0px;">
        <div class="container-fluid" style="padding:0px 0px 0px 0px">
            <div class="col-xs-12 col-sm-12 no-padding">
                 
                 <img src="assets/images/payment.jpg">
                    
            </div>
        </div>
    </div>
 
 
    <div class="copyright-bar" style="background: #020303;height: 60px;">
       <p class="desktop" style="color: white; font-family: 'Open Sans', sans-serif; text-align: center">
            Copyright © 2020-2022. Hayward University . All Rights Reserved. Design And Developed By <a target="_blank" href="http://www.iwebnext.com" style="color: white; font-weight: 700;">IWebNext</a>.
        </p>      
    </div>

</footer>


<style>

    .twitter {
        color: #000000;
        text-decoration: none;
        display: block;
        padding: 14px;
        -webkit-transition: all .25s ease;
        -moz-transition: all .25s ease;
        -ms-transition: all .25s ease;
        -o-transition: all .25s ease;
        transition: all .25s ease;
    }

    .twitter:hover {
        color: #FF7D6D;
        text-decoration: none;
    }


    /* Floating Social Media Bar Style Starts Here */

    .fl-fl {
        background: #464646;
        text-transform: uppercase;
        letter-spacing: 3px;
        padding: 4px;
        width: 190px;
        position: fixed;
        right: -160px;
        z-index: 1000;
        font: normal normal 10px Arial;
        -webkit-transition: all .25s ease;
        -moz-transition: all .25s ease;
        -ms-transition: all .25s ease;
        -o-transition: all .25s ease;
        transition: all .25s ease;
        border-radius: 24px;
    }

    .fa {
        font-size: 20px;
        color: #fff;
        padding: 10px 0;
        width: 40px;
        margin-left: 0px;
    }

    .fl-fl:hover {
        right: 0;
    }

    .fl-fl a {
        color: #fff !important;
        text-decoration: none;
        text-align: center;
        line-height: 43px!important;
        vertical-align: top!important;
    }

    .float-fb {
        top: 160px;
    }

    .float-tw {
        top: 215px;
    }

    .float-gp {
        top: 270px;
    }

    .float-rs {
        top: 325px;
    }

    .float-ig {
        top: 380px;
    }

    .float-pn {
        top: 435px;
    }
    /* Floating Social Media Bar Style Ends Here */
</style>
<!-- <style type="text/css">
    #scrollUp {
    background-color: #666666;
    bottom: 30px;
    color: #ffffff;
    /*font-size: 25px;*/
    height: 50px;
    line-height: 50px;
    right: 30px;
    text-align: center;
    /*transition: all 0.3s ease-in-out 0s;
    transition-property: all;
    transition-duration: 0.3s;
    transition-timing-function: ease-in-out;
    transition-delay: 0s;
    width: 50px;*/
}
</style> -->

<!-- New Code starts Here -->

<style>
    /* Chat Launcher Container */
    .chat-launcher-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 9998;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    /* Robot Icon Button */
    #chat-launcher-btn {
        width: 70px;
        height: 70px;
        background: linear-gradient(135deg, #4a4a4a 0%, #2a2a2a 100%);
        border: 3px solid #666666;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease-in-out;
        order: 2; /* Robot appears on the right */
        position: relative;
    }

    #chat-launcher-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 20px rgba(0,0,0,0.4);
        background: linear-gradient(135deg, #5a5a5a 0%, #3a3a3a 100%);
        border-color: #777777;
    }

    .robot-icon {
        width: 45px;
        height: 45px;
        fill: #ffffff;
        filter: drop-shadow(1px 1px 2px rgba(0,0,0,0.3));
    }

    /* Speech Bubble for Text */
    .chat-text-bubble {
        background-color: #ffffff;
        color: #333333;
        padding: 12px 16px;
        border-radius: 20px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        font-family: 'Arial', sans-serif;
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        position: relative;
        order: 1; /* Text appears on the left */
        cursor: pointer;
        transition: all 0.3s ease-in-out;
        border: 2px solid #e0e0e0;
        opacity: 1;
        transform: translateX(0);
        pointer-events: auto;
    }

    /* Hide text bubble when chat is open */
    .chat-text-bubble.hidden {
        opacity: 0;
        transform: translateX(-20px);
        pointer-events: none;
    }

    .chat-text-bubble:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.25);
        border-color: #333333;
    }

    /* Speech bubble arrow pointing to robot */
    .chat-text-bubble::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -8px;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid #ffffff;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
    }

    .chat-text-bubble::before {
        content: '';
        position: absolute;
        top: 50%;
        right: -10px;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 10px solid #e0e0e0;
        border-top: 10px solid transparent;
        border-bottom: 10px solid transparent;
    }

    .chat-text-bubble:hover::before {
        border-left-color: #333333;
    }

    /* Mobile Responsive Design */
    @media (max-width: 768px) {
        .chat-launcher-container {
            bottom: 15px;
            right: 15px;
            gap: 10px;
        }

        #chat-launcher-btn {
            width: 60px;
            height: 60px;
        }

        .robot-icon {
            width: 32px;
            height: 32px;
        }

        .chat-text-bubble {
            font-size: 13px;
            padding: 10px 14px;
            max-width: 180px;
        }

        .chat-text-bubble::after {
            right: -6px;
            border-left: 6px solid #ffffff;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }

        .chat-text-bubble::before {
            right: -8px;
            border-left: 8px solid #e0e0e0;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
    }

    /* Chat Widget Container */
    .chat-widget {
        position: fixed;
        bottom: 90px; /* Position above the launcher icon */
        right: 20px;
        width: 350px;
        border: 1px solid #ccc;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        flex-direction: column;
        font-family: sans-serif;
        z-index: 9999;
        background-color: #fff;
        /* Animation properties */
        transform-origin: bottom right;
        transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
        /* Initially hidden */
        transform: scale(0);
        opacity: 0;
        pointer-events: none;
    }

    /* Class to show the widget */
    .chat-widget.open {
        transform: scale(1);
        opacity: 1;
        pointer-events: auto;
    }

    .chat-header {
        position: relative; /* Needed for positioning the close button */
        background-color: #202020;
        color: white;
        padding: 10px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chat-header-content {
        display: flex;
        align-items: center;
    }

    .chat-header-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        fill: white;
    }
    
    /* Close Button */
    #chat-close-btn {
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #ffffff;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
    }

    .chat-body {
        height: 300px;
        overflow-y: auto;
        padding: 10px;
        display: flex;
        flex-direction: column;
        background-color: #f9f9f9;
    }
    .chat-message {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 20px;
        max-width: 80%;
        line-height: 1.4;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    .user-message {
        background-color: #ffda00;
        color: black;
        align-self: flex-end;
    }
    .bot-message {
        background-color: #e9e9eb;
        color: black;
        align-self: flex-start;
    }
    .chat-footer {
        display: flex;
        padding: 10px;
        border-top: 1px solid #ccc;
    }
    .chat-footer input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ccc;
        border-radius: 20px;
    }
    .chat-footer button {
        margin-left: 10px;
        padding: 8px 12px;
        border: none;
        background-color: #202020;
        color: white;
        border-radius: 20px;
        cursor: pointer;
    }
</style>

<div class="chat-launcher-container">
    <div class="chat-text-bubble">
        What can I help you with?
    </div>
    <button id="chat-launcher-btn">
        <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
            <!-- Robot head background -->
            <rect x="20" y="25" width="60" height="45" rx="8" ry="8" fill="#ffffff" stroke="#e0e0e0" stroke-width="2"/>

            <!-- Robot eyes -->
            <circle cx="35" cy="40" r="6" fill="#333333"/>
            <circle cx="65" cy="40" r="6" fill="#333333"/>

            <!-- Robot eye highlights -->
            <circle cx="37" cy="38" r="2" fill="#ffffff"/>
            <circle cx="67" cy="38" r="2" fill="#ffffff"/>

            <!-- Robot mouth -->
            <rect x="42" y="55" width="16" height="4" rx="2" fill="#333333"/>

            <!-- Robot antenna -->
            <circle cx="50" cy="15" r="3" fill="#ffdd00"/>
            <rect x="49" y="18" width="2" height="7" fill="#cccccc"/>

            <!-- Robot body -->
            <rect x="30" y="70" width="40" height="20" rx="4" fill="#ffffff" stroke="#e0e0e0" stroke-width="2"/>

            <!-- Robot arms -->
            <rect x="15" y="75" width="12" height="6" rx="3" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>
            <rect x="73" y="75" width="12" height="6" rx="3" fill="#ffffff" stroke="#e0e0e0" stroke-width="1"/>

            <!-- Robot body details -->
            <rect x="35" y="78" width="8" height="3" rx="1" fill="#333333"/>
            <rect x="57" y="78" width="8" height="3" rx="1" fill="#333333"/>
            <circle cx="45" cy="85" r="2" fill="#00ff00"/>
            <circle cx="55" cy="85" r="2" fill="#ff0000"/>
        </svg>
    </button>
</div>

<div class="chat-widget" id="chat-widget">
    <div class="chat-header">
        <div class="chat-header-content">
            <svg class="chat-header-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <!-- Robot head -->
                <rect x="6" y="4" width="12" height="10" rx="2" ry="2" fill="currentColor"/>
                <!-- Robot eyes -->
                <circle cx="9" cy="8" r="1.5" fill="#333"/>
                <circle cx="15" cy="8" r="1.5" fill="#333"/>
                <!-- Robot mouth -->
                <rect x="10" y="11" width="4" height="1" rx="0.5" fill="#333"/>
                <!-- Robot antenna -->
                <circle cx="12" cy="2" r="1" fill="currentColor"/>
                <line x1="12" y1="3" x2="12" y2="4" stroke="currentColor" stroke-width="1"/>
                <!-- Robot body -->
                <rect x="8" y="14" width="8" height="6" rx="1" fill="currentColor"/>
                <!-- Robot arms -->
                <rect x="4" y="15" width="3" height="1" rx="0.5" fill="currentColor"/>
                <rect x="17" y="15" width="3" height="1" rx="0.5" fill="currentColor"/>
            </svg>
            <h3>Chat Assistant</h3>
        </div>
        <button id="chat-close-btn">&times;</button>
    </div>
    <div class="chat-body" id="chat-body">
        <div class="chat-message bot-message">Hello! How can I help you today?</div>
    </div>
    <div class="chat-footer">
        <input type="text" id="user-input" placeholder="Type a message...">
        <button id="send-btn">Send</button>
    </div>
</div>

<script>
    (function() {
        // --- Get all the necessary elements ---
        const launcherBtn = document.getElementById('chat-launcher-btn');
        const chatWidget = document.getElementById('chat-widget');
        const closeBtn = document.getElementById('chat-close-btn');
        const chatBody = document.getElementById('chat-body');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');
        const textBubble = document.querySelector('.chat-text-bubble');

        // --- Function to toggle chat widget ---
        window.toggleChat = function() {
            const isOpen = chatWidget.classList.contains('open');

            if (isOpen) {
                // Close chat
                chatWidget.classList.remove('open');
                // Show text bubble after a short delay
                setTimeout(() => {
                    textBubble.classList.remove('hidden');
                }, 200);
            } else {
                // Open chat
                chatWidget.classList.add('open');
                // Hide text bubble immediately
                textBubble.classList.add('hidden');
            }
        };

        // --- Event Listeners for showing and hiding the widget ---
        launcherBtn.addEventListener('click', () => {
            window.toggleChat();
        });

        textBubble.addEventListener('click', () => {
            window.toggleChat();
        });

        closeBtn.addEventListener('click', () => {
            chatWidget.classList.remove('open');
            // Show text bubble after a short delay
            setTimeout(() => {
                textBubble.classList.remove('hidden');
            }, 200);
        });

        // --- Event Listeners for sending messages ---
        sendBtn.addEventListener('click', sendMessage);
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') sendMessage();
        });

        // --- Core message sending and receiving functions ---
        function sendMessage() {
            const message = userInput.value.trim();
            if (message === '') return;

            appendMessage(message, 'user');
            userInput.value = '';
            userInput.focus();

            fetch('chatbot_backend.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                const reply = data.reply || 'Sorry, an error occurred.';
                appendMessage(reply, 'bot');
            })
            .catch(error => {
                console.error('Fetch error:', error);
                appendMessage('Sorry, I couldn\'t connect to the chat server.', 'bot');
            });
        }

        function appendMessage(message, sender) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('chat-message', `${sender}-message`);
            messageElement.innerText = message;
            chatBody.appendChild(messageElement);
            chatBody.scrollTop = chatBody.scrollHeight;
        }
    })();
</script>

<!-- New Code Ends Here -->

<div class="float-sm">
    <div class="fl-fl float-fb">
        <i class="fa fa-arrow-circle-up" style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a id="scrollUp" href="">Top</a>
    </div>
    <div class="fl-fl float-tw">
        <i class="fa fa-bell" style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a href="notice.php">Notice</a>
    </div>
    <div class="fl-fl float-gp">
        <i class="fa fa-calendar"  style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a href="event.php">Events</a>
    </div>
    <div class="fl-fl float-rs">
        <i class="fa fa-newspaper-o" style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a href="news.php">News</a>
    </div>
    <div class="fl-fl float-ig">
        <i class="fa fa-briefcase" style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a href="career_assistance.php">Career</a>
    </div>
    <div class="fl-fl float-pn">
        <i class="fa fa-question-circle" style="margin-left: 4px;font-size: 18px;color: #ffda00"></i>
        <a href="help-center.php">Help Desk</a>
    </div>
</div>





<script src="assets/js/jquery-1.11.1.min.js"></script>

<script src="assets/js/bootstrap.min.js"></script>

<script src="assets/js/bootstrap-hover-dropdown.min.js"></script>
<script src="assets/js/owl.carousel.min.js"></script>

<script src="assets/js/echo.min.js"></script>
<script src="assets/js/jquery.easing-1.3.min.js"></script>
<script src="assets/js/bootstrap-slider.min.js"></script>
<script src="assets/js/jquery.rateit.min.js"></script>
<script type="text/javascript" src="assets/js/lightbox.min.js"></script>
<script src="assets/js/bootstrap-select.min.js"></script>
<script src="assets/js/wow.min.js"></script>
<script src="assets/js/scripts.js"></script>
<script src="assets/js/jquery.scrollUp.min.js"></script>


</body>
</html>
