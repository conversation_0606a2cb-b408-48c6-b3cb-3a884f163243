<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot Icon with Text</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .robot-container {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            max-width: 400px;
        }

        .robot-button {
            width: 70px;
            height: 70px;
            background-color: #333333;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease-in-out;
            order: 2;
        }

        .robot-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
            background-color: #444444;
        }

        .robot-icon {
            width: 40px;
            height: 40px;
            fill: white;
        }

        .speech-bubble {
            background-color: #ffffff;
            color: #333333;
            padding: 12px 16px;
            border-radius: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            font-size: 16px;
            font-weight: 500;
            position: relative;
            order: 1;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
            border: 2px solid #e0e0e0;
        }

        .speech-bubble:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.25);
            border-color: #333333;
        }

        .speech-bubble::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid #ffffff;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }

        .speech-bubble::before {
            content: '';
            position: absolute;
            top: 50%;
            right: -10px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid #e0e0e0;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }

        .speech-bubble:hover::before {
            border-left-color: #333333;
        }
        
        .demo-section {
            margin: 40px 0;
        }
        
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        /* Different color variations */
        .robot-container.blue {
            background-color: #007bff;
        }
        
        .robot-container.green {
            background-color: #28a745;
        }
        
        .robot-container.purple {
            background-color: #6f42c1;
        }
        
        .robot-container.orange {
            background-color: #fd7e14;
        }
    </style>
</head>
<body>
    <h1>Robot Icon with Text - Demo</h1>
    <p>Here are different variations of the robot icon with text that you can use in your application:</p>
    
    <div class="demo-section">
        <h3>Default Black Theme</h3>
        <div class="robot-container">
            <div class="speech-bubble">
                What can I help you with?
            </div>
            <button class="robot-button">
                <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <!-- Robot head -->
                    <rect x="6" y="4" width="12" height="10" rx="2" ry="2" fill="currentColor"/>
                    <!-- Robot eyes -->
                    <circle cx="9" cy="8" r="1.5" fill="#333"/>
                    <circle cx="15" cy="8" r="1.5" fill="#333"/>
                    <!-- Robot mouth -->
                    <rect x="10" y="11" width="4" height="1" rx="0.5" fill="#333"/>
                    <!-- Robot antenna -->
                    <circle cx="12" cy="2" r="1" fill="currentColor"/>
                    <line x1="12" y1="3" x2="12" y2="4" stroke="currentColor" stroke-width="1"/>
                    <!-- Robot body -->
                    <rect x="8" y="14" width="8" height="6" rx="1" fill="currentColor"/>
                    <!-- Robot arms -->
                    <rect x="4" y="15" width="3" height="1" rx="0.5" fill="currentColor"/>
                    <rect x="17" y="15" width="3" height="1" rx="0.5" fill="currentColor"/>
                </svg>
            </button>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>Color Variations</h3>
        
        <div class="robot-container blue">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#007bff"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#007bff"/>
                <circle cx="67" cy="33" r="2" fill="#007bff"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#007bff"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#007bff" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#007bff"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#007bff"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#007bff"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">Need assistance?</span>
        </div>
        
        <div class="robot-container green">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#28a745"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#28a745"/>
                <circle cx="67" cy="33" r="2" fill="#28a745"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#28a745"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#28a745" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#28a745"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#28a745"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#28a745"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">Chat with us!</span>
        </div>
        
        <div class="robot-container purple">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#6f42c1"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#6f42c1"/>
                <circle cx="67" cy="33" r="2" fill="#6f42c1"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#6f42c1"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#6f42c1" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#6f42c1"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#6f42c1"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#6f42c1"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">How can I help?</span>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>Usage Instructions</h3>
        <p>To use this robot icon in your application:</p>
        <ol>
            <li>Copy the SVG code from the examples above</li>
            <li>Adjust the colors by changing the <code>fill</code> attributes</li>
            <li>Modify the text content as needed</li>
            <li>Apply the CSS styles to match your design</li>
        </ol>
        
        <h4>CSS Classes Available:</h4>
        <ul>
            <li><code>.robot-container</code> - Main container styling</li>
            <li><code>.robot-icon</code> - Icon sizing and spacing</li>
            <li><code>.robot-text</code> - Text styling</li>
            <li><code>.robot-container.blue</code> - Blue color theme</li>
            <li><code>.robot-container.green</code> - Green color theme</li>
            <li><code>.robot-container.purple</code> - Purple color theme</li>
        </ul>
    </div>
</body>
</html>
