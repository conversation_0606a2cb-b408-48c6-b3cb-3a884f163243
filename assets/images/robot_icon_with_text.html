<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robot Icon with Text</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .robot-container {
            display: flex;
            align-items: center;
            background-color: #333333;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .robot-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.4);
            background-color: #444444;
        }
        
        .robot-icon {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .robot-text {
            font-size: 16px;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .demo-section {
            margin: 40px 0;
        }
        
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        /* Different color variations */
        .robot-container.blue {
            background-color: #007bff;
        }
        
        .robot-container.green {
            background-color: #28a745;
        }
        
        .robot-container.purple {
            background-color: #6f42c1;
        }
        
        .robot-container.orange {
            background-color: #fd7e14;
        }
    </style>
</head>
<body>
    <h1>Robot Icon with Text - Demo</h1>
    <p>Here are different variations of the robot icon with text that you can use in your application:</p>
    
    <div class="demo-section">
        <h3>Default Black Theme</h3>
        <div class="robot-container">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <!-- Background circle -->
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                
                <!-- Robot head -->
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#333333"/>
                
                <!-- Robot eyes -->
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                
                <!-- Robot eye highlights -->
                <circle cx="37" cy="33" r="2" fill="#333333"/>
                <circle cx="67" cy="33" r="2" fill="#333333"/>
                
                <!-- Robot mouth -->
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                
                <!-- Robot antenna -->
                <circle cx="50" cy="12" r="3" fill="#333333"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#333333" stroke-width="3"/>
                
                <!-- Robot body -->
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#333333"/>
                
                <!-- Robot arms -->
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#333333"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#333333"/>
                
                <!-- Robot body details -->
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">What can I help you with?</span>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>Color Variations</h3>
        
        <div class="robot-container blue">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#007bff"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#007bff"/>
                <circle cx="67" cy="33" r="2" fill="#007bff"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#007bff"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#007bff" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#007bff"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#007bff"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#007bff"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">Need assistance?</span>
        </div>
        
        <div class="robot-container green">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#28a745"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#28a745"/>
                <circle cx="67" cy="33" r="2" fill="#28a745"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#28a745"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#28a745" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#28a745"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#28a745"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#28a745"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">Chat with us!</span>
        </div>
        
        <div class="robot-container purple">
            <svg class="robot-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
                <circle cx="50" cy="50" r="45" fill="#ffffff" stroke="#cccccc" stroke-width="2"/>
                <rect x="25" y="20" width="50" height="40" rx="8" ry="8" fill="#6f42c1"/>
                <circle cx="35" cy="35" r="6" fill="#ffffff"/>
                <circle cx="65" cy="35" r="6" fill="#ffffff"/>
                <circle cx="37" cy="33" r="2" fill="#6f42c1"/>
                <circle cx="67" cy="33" r="2" fill="#6f42c1"/>
                <rect x="40" y="48" width="20" height="4" rx="2" fill="#ffffff"/>
                <circle cx="50" cy="12" r="3" fill="#6f42c1"/>
                <line x1="50" y1="15" x2="50" y2="20" stroke="#6f42c1" stroke-width="3"/>
                <rect x="30" y="60" width="40" height="25" rx="4" fill="#6f42c1"/>
                <rect x="15" y="65" width="12" height="4" rx="2" fill="#6f42c1"/>
                <rect x="73" y="65" width="12" height="4" rx="2" fill="#6f42c1"/>
                <rect x="35" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <rect x="57" y="68" width="8" height="3" rx="1" fill="#ffffff"/>
                <circle cx="46" cy="76" r="2" fill="#ffffff"/>
                <circle cx="54" cy="76" r="2" fill="#ffffff"/>
            </svg>
            <span class="robot-text">How can I help?</span>
        </div>
    </div>
    
    <div class="demo-section">
        <h3>Usage Instructions</h3>
        <p>To use this robot icon in your application:</p>
        <ol>
            <li>Copy the SVG code from the examples above</li>
            <li>Adjust the colors by changing the <code>fill</code> attributes</li>
            <li>Modify the text content as needed</li>
            <li>Apply the CSS styles to match your design</li>
        </ol>
        
        <h4>CSS Classes Available:</h4>
        <ul>
            <li><code>.robot-container</code> - Main container styling</li>
            <li><code>.robot-icon</code> - Icon sizing and spacing</li>
            <li><code>.robot-text</code> - Text styling</li>
            <li><code>.robot-container.blue</code> - Blue color theme</li>
            <li><code>.robot-container.green</code> - Green color theme</li>
            <li><code>.robot-container.purple</code> - Purple color theme</li>
        </ul>
    </div>
</body>
</html>
